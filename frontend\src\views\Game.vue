<template>
  <div class="game-page">
    <!-- 通用导航栏 -->
    <CommonNavbar>
      <template #left>
        <div class="player-info">
        </div>
      </template>
    </CommonNavbar>

    <!-- 游戏主体区域 -->
    <div class="game-main">
      <!-- 游戏棋盘区域 -->
      <div class="game-board-section">


        <!-- 棋盘和公告栏容器 -->
        <div class="board-announcement-container">
          <!-- 左侧版本选择面板 -->
          <div class="version-selector-panel">
            <div class="selector-card">
              <div class="selector-header">
                <h3>🎯 选择版本</h3>
              </div>
              <div class="selector-options">
                <div v-for="version in sortedVersions" :key="version.version" class="selector-option"
                  :class="{ active: gameStore.currentVersion === version.version }"
                  @click="selectVersion(version.version)">
                  <div class="option-icon">{{ getVersionIcon(version.version) }}</div>
                  <div class="option-info">
                    <div class="option-name">{{ version.name }}</div>
                    <div class="option-size">{{ version.size }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div>
            <!-- 游戏棋盘 -->
            <div class="game-board-container" ref="boardContainer">
              <div v-if="gameStore.boardState" class="game-board" ref="gameBoard">
                <div v-for="(row, y) in gameStore.boardGrid" :key="y" class="board-row">
                  <div v-for="(cell, x) in row" :key="x" class="board-cell" :class="{
                    'has-item': cell && !cell.isRemoved,
                    'selected': isSelected(cell),
                    'large-item': cell && (cell.width > 1 || cell.height > 1),
                    'main-cell': cell && cell.x === x && cell.y === y
                  }" :style="{
                    width: cellSize + 'px',
                    height: cellSize + 'px'
                  }" @click="selectItem(cell, x, y)">
                    <div v-if="cell && !cell.isRemoved && cell.x === x && cell.y === y" class="game-item">
                      <img :src="getItemImage(cell)" :alt="cell.name" class="item-image" :style="getItemImageStyle(cell)" />
                    </div>
                  </div>
                </div>
              </div>

              <div v-else class="loading-board">
                <div class="spinner"></div>
                <p>加载游戏中...</p>
              </div>

              <!-- 连线动画组件 -->
              <ConnectionLine :connection-line="gameStore.connectionLine" :cell-size="cellSize"
                :board-offset="boardOffset" :board-padding="boardPadding" />

              <!-- 消除动画组件 -->
              <RemoveAnimation :removing-items="gameStore.removingItems" :cell-size="cellSize"
                :board-offset="boardOffset" />
            </div>
            <!-- 游戏控制面板 -->
            <div class="game-bottom-controls">
              <button @click="undoMove" class="btn-control btn-undo" :disabled="!gameStore.canUndo"
                :title="gameStore.canUndo ? `撤销: ${gameStore.lastMoveDescription} (Ctrl+Z)` : '没有可撤销的操作'">
                <span class="undo-icon">↶</span>
                撤销 ({{ gameStore.undoStepsCount }})
              </button>
              <button @click="resetGame" class="btn-control">重置</button>
              <button @click="saveGame" class="btn-control">保存</button>
              <button @click="showImportDialog = true" class="btn-control">导入</button>
              <button @click="shareGame" class="btn-control btn-share"
                title="分享游戏进度链接 (Ctrl+S)" :disabled="gameStore.isSharing">
                <span class="share-icon">📤</span>
                {{ gameStore.isSharing ? '分享中...' : '分享' }}
              </button>
            </div>
          </div>
          <!-- 右侧面板容器 -->
          <div class="right-panels">
            <!-- 公告栏 -->
            <div class="announcement-panel">
              <div class="announcement-header">
                <h3>小里连连看规则</h3>
              </div>
              <div class="announcement-content">
                <div class="rule-item">
                  <p>每局结束连线，带出几格红就可连几个</p>
                </div>
                <div class="rule-item">
                  <p>6格以上的红可以存一个，其他格数用不完则白送</p>
                </div>
                <div class="rule-item">
                  <p>大格红可以降级连小格，小格红不能连大格</p>
                </div>
                <div class="rule-item">
                  <p>如6格红可以连3对1格</p>
                </div>
                <div class="rule-item">
                  <p style="color: red;">注意：连线大格红需要清空其连线路径上的所有小格</p>
                </div>
              </div>
            </div>

            <!-- 暂存大红面板 -->
            <div class="stored-red-panel">
              <div class="stored-red-header">
                <h3>暂存大红</h3>
              </div>
              <div class="stored-red-content">
                <div class="red-item">
                  <span class="red-label">6格:</span>
                  <button @click="gameStore.decreaseStoredRed('red6')" class="btn-adjust"
                    :disabled="gameStore.storedRed.red6 <= 0">-</button>
                  <span class="red-count">({{ gameStore.storedRed.red6 }})</span>
                  <button @click="gameStore.increaseStoredRed('red6')" class="btn-adjust">+</button>
                </div>
                <div class="red-item">
                  <span class="red-label">9格:</span>
                  <button @click="gameStore.decreaseStoredRed('red9')" class="btn-adjust"
                    :disabled="gameStore.storedRed.red9 <= 0">-</button>
                  <span class="red-count">({{ gameStore.storedRed.red9 }})</span>
                  <button @click="gameStore.increaseStoredRed('red9')" class="btn-adjust">+</button>
                </div>
                <div class="red-item">
                  <span class="red-label">12格:</span>
                  <button @click="gameStore.decreaseStoredRed('red12')" class="btn-adjust"
                    :disabled="gameStore.storedRed.red12 <= 0">-</button>
                  <span class="red-count">({{ gameStore.storedRed.red12 }})</span>
                  <button @click="gameStore.increaseStoredRed('red12')" class="btn-adjust">+</button>
                </div>
              </div>
            </div>
          </div>
        </div>


      </div>
    </div>
  </div>

  <!-- 游戏完成提示 -->
  <div v-if="gameStore.isGameCompleted" class="game-complete">
    <div class="complete-dialog">
      <h2>🎉 恭喜完成!</h2>
      <div class="complete-stats">
        <div class="stat-item">
          <span class="stat-label">得分:</span>
          <span class="stat-value">{{ gameStore.score }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">难度:</span>
          <span class="stat-value">{{ gameStore.difficultyName }}</span>
        </div>
      </div>
      <div class="complete-buttons">
        <button @click="shareGame" class="btn-share-complete" :disabled="gameStore.isSharing">
          📤 {{ gameStore.isSharing ? '分享中...' : '分享进度' }}
        </button>
        <button @click="resetGame" class="btn-confirm">再来一局</button>
        <button @click="goHome" class="btn-cancel">返回首页</button>
      </div>
    </div>
  </div>

  <!-- 导入对话框 -->
  <div v-if="showImportDialog" class="dialog-overlay" @click="closeImportDialog">
    <div class="dialog" @click.stop>
      <h3>导入游戏进度</h3>
      <input v-model="importCode" type="text" placeholder="请输入进度代码" @keyup.enter="importGame" />
      <div class="dialog-buttons">
        <button @click="closeImportDialog" class="btn-cancel">取消</button>
        <button @click="importGame" class="btn-confirm" :disabled="!importCode.trim()">
          导入
        </button>
      </div>
    </div>
  </div>

  <!-- 保存成功提示 -->
  <div v-if="showSaveSuccess" class="save-success">
    <div class="success-content">
      <h3>保存成功!</h3>
      <p>进度代码: <strong>{{ savedCode }}</strong></p>
      <button @click="copySaveCode" class="btn-copy">复制代码</button>
      <button @click="showSaveSuccess = false" class="btn-close">关闭</button>
    </div>
  </div>


</template>

<script>
import { ref, onMounted, onUnmounted, computed, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useGameStore } from '../stores/gameStore'
import { copyTextWithFeedback } from '../utils/clipboard'
import ConnectionLine from '../components/ConnectionLine.vue'
import RemoveAnimation from '../components/RemoveAnimation.vue'
import CommonNavbar from '../components/CommonNavbar.vue'

export default {
  name: 'Game',
  components: {
    ConnectionLine,
    RemoveAnimation,
    CommonNavbar
  },
  props: {
    sessionId: String
  },
  setup(props) {
    const route = useRoute()
    const router = useRouter()
    const gameStore = useGameStore()

    const showImportDialog = ref(false)
    const showSaveSuccess = ref(false)
    const importCode = ref('')
    const savedCode = ref('')
    const boardContainer = ref(null)
    const gameBoard = ref(null)

    // 窗口大小状态
    const windowWidth = ref(window.innerWidth)
    const windowHeight = ref(window.innerHeight)

    // 强制更新boardOffset的触发器
    const forceUpdateTrigger = ref(0)

    // 计算棋盘偏移
    const boardOffset = computed(() => {
      // 依赖forceUpdateTrigger来强制重新计算
      forceUpdateTrigger.value

      if (!gameBoard.value) {
        return { x: 0, y: 0 }
      }

      const boardRect = gameBoard.value.getBoundingClientRect()
      const result = {
        x: boardRect.left,
        y: boardRect.top
      }

      // 调试信息
    

      return result
    })

    // 计算格子大小（响应式）
    const cellSize = computed(() => {
      // 根据屏幕宽度和棋盘大小动态计算
      const boardWidth = gameStore.boardState?.width || 10
      const boardHeight = gameStore.boardState?.height || 9

      if (windowWidth.value <= 768) {
        // 移动端：根据棋盘大小调整
        const availableWidth = windowWidth.value - 40 // 预留边距
        const maxCellWidth = Math.floor(availableWidth / boardWidth) - 2

        if (boardWidth >= 15 || boardHeight >= 15) {
          return Math.max(Math.min(35, maxCellWidth), 25) // 困难版移动端，最小25px
        } else if (boardWidth >= 10 || boardHeight >= 9) {
          return Math.max(Math.min(50, maxCellWidth), 35) // 普通版移动端
        }
        return Math.max(Math.min(60, maxCellWidth), 40) // 简单版移动端
      } else {
        // 桌面端：根据棋盘大小和屏幕宽度调整
        let availableWidth
        if (windowWidth.value <= 1600) {
          // 中等屏幕：面板会移到下方，可以使用更多宽度
          availableWidth = windowWidth.value - 100
        } else {
          // 大屏幕：面板在右侧，需要预留空间
          availableWidth = windowWidth.value - 650
        }

        const maxCellWidth = Math.floor(availableWidth / boardWidth) - 4 // 减去边框和间距

        if (boardWidth >= 15 || boardHeight >= 15) {
          // 困难版：限制最大格子大小
          return Math.max(Math.min(55, maxCellWidth), 35)
        } else if (boardWidth >= 10 || boardHeight >= 9) {
          // 普通版
          return Math.max(Math.min(70, maxCellWidth), 50)
        }
        // 简单版
        return Math.max(Math.min(80, maxCellWidth), 60)
      }
    })

    // 计算棋盘padding（响应式）
    const boardPadding = computed(() => {
      // 根据屏幕大小动态计算padding
      const result = windowWidth.value <= 768 ? 16 : 32

  

      return result
    })

    // 窗口大小变化处理
    const handleResize = () => {
      windowWidth.value = window.innerWidth
      windowHeight.value = window.innerHeight
      // 强制更新boardOffset
      forceUpdateTrigger.value++
    }

    // 滚动处理
    const handleScroll = () => {
      // 强制更新boardOffset
      forceUpdateTrigger.value++
    }

    // 键盘事件处理
    const handleKeydown = (event) => {
      // Ctrl+Z 撤销
      if (event.ctrlKey && event.key === 'z' && gameStore.canUndo) {
        event.preventDefault()
        undoMove()
      }

      // Ctrl+S 分享
      if (event.ctrlKey && event.key === 's') {
        event.preventDefault()
        shareGame()
      }
    }

    // 监听棋盘状态变化，强制更新boardOffset
    watch(() => gameStore.boardState, async () => {
      if (gameStore.boardState) {
        // 等待DOM更新完成后重新计算offset
        await nextTick()
        // 强制触发boardOffset重新计算
        forceUpdateTrigger.value++
      }
    }, { deep: true })

    // 监听当前版本变化，强制更新
    watch(() => gameStore.currentVersion, async () => {
      await nextTick()
      // 版本切换时需要等待更长时间，确保DOM完全更新
      setTimeout(() => {
        forceUpdateTrigger.value++
      }, 100)
    })

    // 监听cellSize变化，强制更新boardOffset
    watch(() => cellSize.value, async () => {
      await nextTick()
      // 等待额外的时间确保DOM完全更新
      setTimeout(() => {
        forceUpdateTrigger.value++
      }, 50)
    })

    // 监听boardPadding变化，强制更新boardOffset
    watch(() => boardPadding.value, async () => {
      await nextTick()
      setTimeout(() => {
        forceUpdateTrigger.value++
      }, 50)
    })

    // 初始化游戏
    onMounted(async () => {
      // 首先加载可用版本
      await gameStore.loadAvailableVersions()

      const sessionId = props.sessionId || route.params.sessionId

      if (sessionId && sessionId !== 'undefined') {
        try {
          await gameStore.loadGameSession(sessionId)
        } catch (error) {
          console.error('加载游戏会话失败:', error)
          alert('游戏会话不存在或已过期，将返回首页')
          router.push('/')
        }
      } else {
        // 没有会话ID，加载默认版本（简单版）
        try {
          await gameStore.switchVersion('simple')
        } catch (error) {
          console.error('加载默认版本失败:', error)
          router.push('/')
        }
      }

      // 添加事件监听
      document.addEventListener('keydown', handleKeydown)
      window.addEventListener('resize', handleResize)
      window.addEventListener('scroll', handleScroll)

      // 初始化完成后强制更新一次boardOffset
      await nextTick()
      forceUpdateTrigger.value++

      // 注册强制刷新回调
      gameStore.setForceRefreshCallback(() => {
        forceUpdateTrigger.value++
      })
    })

    // 组件卸载时清理事件监听
    onUnmounted(() => {
      document.removeEventListener('keydown', handleKeydown)
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('scroll', handleScroll)
    })

    // 选择物品
    const selectItem = (cell, x, y) => {
      if (!cell || cell.isRemoved) return

      // 对于大格物品，允许点击任何部分都能选中
      // 对于单格物品，只能点击自己
      if (cell.width > 1 || cell.height > 1) {
        // 大格物品：点击任何部分都可以选中
        gameStore.selectItem(cell)
      } else {
        // 单格物品：只能点击自己的位置
        if (cell.x === x && cell.y === y) {
          gameStore.selectItem(cell)
        }
      }
    }

    // 检查物品是否被选中
    const isSelected = (cell) => {
      if (!cell) return false
      return gameStore.selectedItems.some(item => item.id === cell.id)
    }

    // 获取物品图片
    const getItemImage = (item) => {
      // 使用本地图片资源
      return `/images/${item.name}.png`
    }

    // 获取物品样式（用于大物体）
    const getItemStyle = (item) => {
      if (item.width === 1 && item.height === 1) {
        return {}
      }

      return {
        width: `${item.width * cellSize.value - 2}px`,
        height: `${item.height * cellSize.value - 2}px`,
        position: 'absolute',
        top: '1px',
        left: '1px',
        zIndex: 10
      }
    }

    // 获取物品图片样式（动态调整图片大小）
    const getItemImageStyle = (item) => {
      const imageSize = Math.max(cellSize.value - 10, 20) // 图片比格子小10px，最小20px

      if (item.width === 1 && item.height === 1) {
        // 单格物品
        return {
          width: `${imageSize}px`,
          height: `${imageSize}px`
        }
      } else {
        // 多格物品
        return {
          width: `${item.width * cellSize.value - 12}px`,
          height: `${item.height * cellSize.value - 12}px`,
          position: 'absolute',
          top: '1px',
          left: '1px',
          zIndex: 10
        }
      }
    }

    // 选择版本
    const selectVersion = async (version) => {
      if (version === gameStore.currentVersion) return

      try {
        await gameStore.switchVersion(version)
      } catch (error) {
        console.error('切换版本失败:', error)
        alert('切换版本失败: ' + error.message)
      }
    }

    // 版本排序（简单-普通-困难）
    const sortedVersions = computed(() => {
      if (!gameStore.availableVersions) return []

      return [...gameStore.availableVersions].sort((a, b) => {
        const order = { 'simple': 1, 'normal': 2, 'hard': 3 }
        return (order[a.version] || 999) - (order[b.version] || 999)
      })
    })

    // 获取版本图标
    const getVersionIcon = (version) => {
      const icons = {
        'simple': '🟢',
        'normal': '🟡',
        'hard': '🔴'
      }
      return icons[version] || '⚪'
    }

    // 撤销操作
    const undoMove = () => {
      const success = gameStore.undoLastMove()
      if (!success) {
        // 可以添加一个提示，但通常按钮已经禁用了
      }
    }

    // 重置游戏
    const resetGame = async () => {
      if (confirm('确定要重置游戏吗？')) {
        await gameStore.resetGame()
      }
    }

    // 保存游戏
    const saveGame = async () => {
      try {
        const progressCode = await gameStore.exportGameProgress()
        savedCode.value = progressCode
        showSaveSuccess.value = true
      } catch (error) {
        alert('保存失败: ' + error.message)
      }
    }

    // 导入游戏
    const importGame = async () => {
      if (!importCode.value.trim()) {
        alert('请输入进度代码')
        return
      }

      try {
        await gameStore.importGameProgress(importCode.value.trim())
        alert('导入成功!')
        closeImportDialog()
      } catch (error) {
        alert('导入失败: ' + error.message)
      }
    }

    // 复制保存代码
    const copySaveCode = async () => {
      await copyTextWithFeedback(
        savedCode.value,
        '代码已复制到剪贴板！',
        '复制失败，请手动复制代码'
      )
    }

    // 关闭导入对话框
    const closeImportDialog = () => {
      showImportDialog.value = false
      importCode.value = ''
    }

    // 返回首页
    const goHome = () => {
      gameStore.clearGame()
      router.push('/')
    }

    // 分享游戏进度
    const shareGame = async () => {
      await gameStore.shareGameProgress()
    }

    return {
      gameStore,
      showImportDialog,
      showSaveSuccess,
      importCode,
      savedCode,
      boardContainer,
      gameBoard,
      boardOffset,
      cellSize,
      boardPadding,
      selectItem,
      isSelected,
      getItemImage,
      getItemStyle,
      getItemImageStyle,
      selectVersion,
      sortedVersions,
      getVersionIcon,
      undoMove,
      resetGame,
      saveGame,
      importGame,
      copySaveCode,
      closeImportDialog,
      goHome,
      shareGame
    }
  }
}
</script>

<style scoped>
.game-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  /* 使用flex布局让内容紧凑排列 */
  display: flex;
  flex-direction: column;
}

.game-main {
  padding: 20px 10px 10px 10px;
  flex: 1;
}



.player-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.player-name {
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

.score {
  color: #4CAF50;
}



.btn-control {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 8px;
  background: #667eea;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-control:hover {
  background: #5a6fd8;
  transform: translateY(-2px);
}

.btn-control:disabled {
  background: #ccc;
  color: #999;
  cursor: not-allowed;
  transform: none;
}

.btn-control:disabled:hover {
  background: #ccc;
  transform: none;
}

.btn-undo {
  background: #ff9800;
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.btn-undo:hover:not(:disabled) {
  background: #f57c00;
}

.btn-undo:disabled {
  background: #ccc;
}

.undo-icon {
  font-size: 1.2em;
  font-weight: bold;
}



.btn-share {
  background: #4CAF50;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.btn-share:hover {
  background: #45a049;
}

.share-icon {
  font-size: 1em;
}

.game-main {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  /* 让主体区域更紧凑，减少不必要的空间 */
  min-height: 0;
  /* 移除flex: 1，让内容决定高度 */
  height: fit-content;
}

.game-board-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  /* 让棋盘区域更紧凑，减少垂直间距 */
  gap: 0.5rem;
  justify-content: flex-start;
  /* 移除多余的高度，让内容紧凑排列 */
  height: fit-content;
  min-height: auto;
}

.board-announcement-container {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
  /* 让容器更紧凑，自动调整高度 */
  justify-content: center;
  flex-wrap: wrap;
  /* 移除固定高度，让内容决定容器大小 */
  height: fit-content;
  min-height: auto;
}

/* 版本选择面板样式 */
.version-selector-panel {
  min-width: 200px;
  max-width: 250px;
}

.selector-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 2px solid #e0e0e0;
}

.selector-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem;
  text-align: center;
}

.selector-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.selector-options {
  padding: 0.5rem;
}

.selector-option {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  margin: 0.25rem 0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.selector-option:hover {
  background: #f5f5f5;
  transform: translateX(2px);
}

.selector-option.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: #5a6fd8;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.option-icon {
  font-size: 1.5rem;
  margin-right: 0.75rem;
  min-width: 2rem;
  text-align: center;
}

.option-info {
  flex: 1;
}

.option-name {
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 0.2rem;
}

.option-size {
  font-size: 0.75rem;
  opacity: 0.8;
}

/* 右侧面板容器 */
.right-panels {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  min-width: 250px;
}

/* 公告栏样式 */
.announcement-panel {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 12px;
  padding: 1rem;
  color: white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.announcement-header {
  text-align: center;
  margin-bottom: 1rem;
  border-bottom: 2px solid rgba(255, 255, 255, 0.3);
  padding-bottom: 0.5rem;
}

.announcement-header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: bold;
}

.announcement-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.rule-item {
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  transition: background 0.3s ease;
}

.rule-item:hover {
  background: rgba(255, 255, 255, 0.2);
}

.rule-item p {
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* 暂存大红面板样式 */
.stored-red-panel {
  background: linear-gradient(135deg, #fafafa 0%, #ffffff 100%);
  border-radius: 12px;
  padding: 1rem;
  min-width: 200px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.stored-red-header {
  text-align: center;
  margin-bottom: 1rem;
  border-bottom: 2px solid rgba(255, 255, 255, 0.3);
  padding-bottom: 0.5rem;
}

.stored-red-header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: bold;
}

.stored-red-content {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.red-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.5rem;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  transition: background 0.3s ease;
}

.red-item:hover {
  background: rgba(255, 255, 255, 0.2);
}

.red-label {
  font-weight: bold;
  min-width: 40px;
}

.red-count {
  font-weight: bold;
  font-size: 1.1rem;
  min-width: 30px;
  text-align: center;
}

.btn-adjust {
  width: 30px;
  height: 30px;
  border: none;
  border-radius: 50%;
  background: #ff9800;
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-adjust:hover:not(:disabled) {
  background: #f57c00;
  transform: scale(1.1);
}

.btn-adjust:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

/* 游戏底部控制面板 */
.game-bottom-controls {
  display: flex;
  justify-content: center;
  gap: 1rem;
  /* 大幅减少margin，让按钮紧贴棋盘 */
  margin-top: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  /* 让按钮容器紧贴棋盘，不占用额外空间 */
  width: fit-content;
  margin-left: auto;
  margin-right: auto;
}

.game-title {
  margin-bottom: 1rem;
  text-align: center;
}

.game-title h1 {
  font-size: 2.5rem;
  font-weight: bold;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  margin: 0;
  background: linear-gradient(45deg, #000000, #000000);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.game-board-container {
  display: flex;
  justify-content: center;
  align-items: center;
  /* 让棋盘容器更紧凑 */
  flex-shrink: 0;
}

.game-board {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.board-row {
  display: flex;
}

.board-cell {
  border: 1px solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: visible;
}

.board-cell.main-cell {
  z-index: 10;
}

.board-cell.has-item {
  background: transparent;
}

.board-cell.selected {
  background: #ffeb3b !important;
  border-color: #ff9800;
  box-shadow: 0 0 10px rgba(255, 152, 0, 0.5);
}

.board-cell:hover.has-item {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.05);
}

.game-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: transparent;
  border-radius: 4px;
  position: relative;
}

.item-image {
  object-fit: cover;
  border-radius: 4px;
}

.loading-board {
  text-align: center;
  color: white;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(255, 255, 255, 0.3);
  border-top: 5px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.game-complete {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.complete-dialog {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  text-align: center;
  min-width: 300px;
}

.complete-dialog h2 {
  color: #4CAF50;
  margin-bottom: 1rem;
}

.complete-stats {
  display: flex;
  justify-content: space-around;
  margin: 1rem 0;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.complete-stats .stat-item {
  text-align: center;
}

.complete-stats .stat-label {
  display: block;
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.25rem;
}

.complete-stats .stat-value {
  display: block;
  font-size: 1.2rem;
  font-weight: bold;
  color: #333;
}

.complete-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 1.5rem;
  flex-wrap: wrap;
}

.btn-share-complete {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  background: #4CAF50;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
}

.btn-share-complete:hover {
  background: #45a049;
  transform: translateY(-2px);
}

.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  min-width: 400px;
  max-width: 90vw;
}

.dialog h3 {
  margin-bottom: 1rem;
  color: #333;
}

.dialog input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  margin-bottom: 1.5rem;
}

.dialog-buttons {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.btn-cancel,
.btn-confirm {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-cancel {
  background: #f5f5f5;
  color: #666;
}

.btn-confirm {
  background: #667eea;
  color: white;
}

.btn-confirm:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.save-success {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.success-content {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  text-align: center;
  min-width: 400px;
}

.success-content h3 {
  color: #4CAF50;
  margin-bottom: 1rem;
}

.success-content p {
  margin-bottom: 1.5rem;
  word-break: break-all;
}

.btn-copy,
.btn-close {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  margin: 0 0.5rem;
}

.btn-copy {
  background: #4CAF50;
  color: white;
}

.btn-close {
  background: #f5f5f5;
  color: #666;
}

/* 公告栏样式 */
.announcement-panel {
  width: 280px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  height: fit-content;
  align-self: flex-start;
}

.announcement-header {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  padding: 1rem;
  text-align: center;
}

.announcement-header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: bold;
}

.announcement-content {
  padding: 1rem;
}

.rule-item {
  margin-bottom: 0.75rem;
  padding: 0.6rem;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 6px;
}

.rule-item:last-child {
  margin-bottom: 0;
}

.rule-item p {
  margin: 0;
  font-size: 0.85rem;
  line-height: 1.4;
  color: #333;
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .board-announcement-container {
    flex-direction: column;
    align-items: center;
  }

  .version-selector-panel {
    width: 100%;
    max-width: 600px;
    margin-bottom: 1rem;
  }

  .selector-options {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .selector-option {
    flex: 1;
    min-width: 200px;
  }

  .announcement-panel {
    width: 100%;
    max-width: 600px;
    margin-top: 2rem;
  }

  .game-title h1 {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .game-title h1 {
    font-size: 1.5rem;
  }

  .game-board {
    padding: 1rem;
  }

  .game-bottom-controls {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .btn-control {
    flex: 1;
    min-width: 80px;
    font-size: 0.85rem;
    padding: 0.4rem 0.6rem;
  }

  .complete-buttons {
    flex-direction: column;
    gap: 0.75rem;
  }

  .btn-share-complete,
  .btn-confirm,
  .btn-cancel {
    width: 100%;
  }
}
</style>
