<template>
  <div class="item-selection-game">
    <!-- 通用导航栏 -->
    <CommonNavbar>
      <template #left>
        <div class="player-info">
          <span class="player-name">玩家: {{ playerName }}</span>
        </div>
      </template>
      <template #right>
        <div class="game-stats">
          <div class="stat-item">
            <span class="stat-label">待确认:</span>
            <span class="stat-value pending">{{ pendingCount }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">已确认:</span>
            <span class="stat-value confirmed">{{ confirmedCount }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">状态:</span>
            <span class="stat-value" :class="{ 'unsaved': hasUnsavedChanges, 'saved': !hasUnsavedChanges }">
              {{ hasUnsavedChanges ? '未保存' : '已保存' }}
            </span>
          </div>
        </div>
      </template>
    </CommonNavbar>

    <!-- 游戏主体区域 -->
    <div class="game-main">
      <!-- 游戏内容容器 -->
      <div class="game-content-container">
        <!-- 左侧难度选择面板 -->
        <div class="difficulty-selector-panel">
          <div class="selector-card">
            <div class="selector-header">
              <h3>🎯 选择难度</h3>
            </div>
            <div class="selector-options">
              <div v-for="level in difficultyLevels" :key="level.level" class="selector-option"
                :class="{ active: currentDifficulty === level.level }"
                @click="selectDifficulty(level.level)">
                <div class="option-icon">{{ getDifficultyIcon(level.level) }}</div>
                <div class="option-info">
                  <div class="option-name">{{ level.name }}</div>
                  <div class="option-desc">{{ level.description }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 右侧游戏区域 -->
        <div class="game-area">
          <!-- 游戏网格 -->
    <div class="game-grid" v-if="items.length > 0">
      <div
        v-for="item in items"
        :key="item.id"
        class="item-card"
        :class="{
          'pending': item.status === 'pending',
          'confirmed': item.status === 'confirmed'
        }"
        :style="{ backgroundImage: `url(${getImageUrl(item)})` }"
        @click="toggleItemStatus(item)"
      >
        <!-- 状态标签 -->
        <div v-if="item.status === 'pending'" class="status-label">
          待确认
        </div>
        
        <!-- 物品名称 -->
        <div class="item-name">{{ item.name }}</div>
      </div>
    </div>

    <!-- 功能按钮 -->
    <div class="game-controls">
      <button 
        class="control-btn confirm-btn" 
        @click="confirmSelection"
        :disabled="pendingCount === 0"
      >
        确认 ({{ pendingCount }})
      </button>
      <button class="control-btn reset-btn" @click="resetGame">
        重置
      </button>
      <button class="control-btn save-btn" @click="exportGame">
        保存
      </button>
      <button class="control-btn import-btn" @click="showImportDialog = true">
        导入
      </button>
      <button class="control-btn share-btn" @click="shareGame" :disabled="isSharing">
        <span class="share-icon">📤</span>
        {{ isSharing ? '分享中...' : '分享' }}
      </button>
    </div>
        </div> <!-- 关闭 game-area -->
      </div> <!-- 关闭 game-content-container -->
    </div> <!-- 关闭 game-main -->

    <!-- 导入对话框 -->
    <div v-if="showImportDialog" class="dialog-overlay" @click="closeImportDialog">
      <div class="dialog" @click.stop>
        <h3>导入游戏状态</h3>
        <textarea
          v-model="importCode"
          placeholder="请粘贴导入代码"
          rows="4"
        ></textarea>
        <div class="dialog-buttons">
          <button @click="closeImportDialog" class="btn-cancel">取消</button>
          <button @click="importGame" class="btn-confirm" :disabled="!importCode.trim()">
            导入
          </button>
        </div>
      </div>
    </div>

    <!-- 保存成功提示 -->
    <div v-if="showSaveSuccess" class="save-success">
      <div class="success-content">
        <h3>保存成功!</h3>
        <p>进度代码: <strong>{{ savedCode }}</strong></p>
        <button @click="copySaveCode" class="btn-copy">复制代码</button>
        <button @click="showSaveSuccess = false" class="btn-close">关闭</button>
      </div>
    </div>

    <!-- 加载中 -->
    <div v-if="isLoading" class="loading">
      <div class="spinner"></div>
      <p>{{ loadingText }}</p>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { itemSelectionApi } from '../api/itemSelectionApi'
import { toast } from '../utils/toast'
import { getPlayerNameOrDefault } from '../utils/playerUtils'
import CommonNavbar from '../components/CommonNavbar.vue'

export default {
  name: 'ItemSelectionGame',
  components: {
    CommonNavbar
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    
    // 响应式数据
    const sessionId = ref('')
    const playerName = ref(getPlayerNameOrDefault('玩家'))
    const items = ref([])
    const isLoading = ref(false)
    const loadingText = ref('加载中...')
    const showImportDialog = ref(false)
    const importCode = ref('')
    const showSaveSuccess = ref(false)
    const savedCode = ref('')

    // 保存状态相关
    const hasUnsavedChanges = ref(false)

    // 分享功能相关
    const isSharing = ref(false)

    // 难度级别相关
    const difficultyLevels = ref([])
    const currentDifficulty = ref('normal')

    // 计算属性
    const pendingCount = computed(() => 
      items.value.filter(item => item.status === 'pending').length
    )
    
    const confirmedCount = computed(() =>
      items.value.filter(item => item.status === 'confirmed').length
    )

    const currentDifficultyName = computed(() => {
      const level = difficultyLevels.value.find(l => l.level === currentDifficulty.value)
      return level ? level.name : '普通版'
    })

    // 方法
    const getImageUrl = (item) => {
      // 直接使用数据库中的imagePath字段
      if (!item || !item.imagePath) {
        return '/images/default-item.png'
      }
      return item.imagePath
    }

    const handleImageError = (event) => {
      // 图片加载失败时显示默认图片
      event.target.src = '/images/default-item.png'
    }

    const toggleItemStatus = (item) => {
      if (item.status === 'confirmed') {
        // 已确认的物品不能再改变状态
        return
      }

      // 切换待确认状态
      item.status = item.status === 'pending' ? 'default' : 'pending'

      // 标记有未保存的更改（但不自动保存）
      hasUnsavedChanges.value = true
    }



    const saveGameState = async () => {
      if (!sessionId.value) {
        console.warn('没有sessionId，无法保存游戏状态')
        return
      }

      try {
        // 保存到后端
        await itemSelectionApi.updateGameState(sessionId.value, items.value)
        hasUnsavedChanges.value = false
        console.log('游戏状态已保存到后端')
      } catch (error) {
        console.error('保存到后端失败，使用本地存储:', error)

        // 后端保存失败，使用localStorage作为备用
        try {
          const gameData = {
            sessionId: sessionId.value,
            playerName: playerName.value,
            items: items.value,
            lastSaved: new Date().toISOString()
          }
          localStorage.setItem(`item-selection-${sessionId.value}`, JSON.stringify(gameData))
          hasUnsavedChanges.value = false
          console.log('游戏状态已保存到本地存储')
        } catch (localError) {
          console.error('本地存储也失败:', localError)
          toast.error('保存失败: ' + error.message)
        }
      }
    }

    const confirmSelection = async () => {
      if (pendingCount.value === 0) {
        toast.warning('没有待确认的物品')
        return
      }

      const currentPendingCount = pendingCount.value

      try {
        isLoading.value = true
        loadingText.value = '确认并保存中...'

        // 调用后端API确认选择
        await itemSelectionApi.confirmSelection(sessionId.value)

        // 更新本地状态
        items.value.forEach(item => {
          if (item.status === 'pending') {
            item.status = 'confirmed'
          }
        })

        // 自动保存游戏状态（使用物品选择游戏专用的保存方式）
        try {
          await saveGameState()
          toast.success(`已确认 ${currentPendingCount} 个物品并保存`)
        } catch (saveError) {
          console.error('自动保存失败:', saveError)
          // 即使保存失败，确认功能仍然成功
          hasUnsavedChanges.value = false
          toast.success(`已确认 ${currentPendingCount} 个物品（保存失败）`)
        }
      } catch (error) {
        console.error('确认选择失败:', error)
        toast.error('确认失败: ' + error.message)
      } finally {
        isLoading.value = false
      }
    }

    const resetGame = async () => {
      if (!confirm('确定要重置游戏吗？这将清除所有选择状态。')) {
        return
      }

      try {
        isLoading.value = true
        loadingText.value = '重置中...'

        // 调用后端API重置游戏
        await itemSelectionApi.resetGame(sessionId.value)

        // 更新本地状态
        items.value.forEach(item => {
          item.status = 'default'
        })

        hasUnsavedChanges.value = false
        toast.success('游戏已重置')
      } catch (error) {
        console.error('重置游戏失败:', error)
        toast.error('重置失败: ' + error.message)
      } finally {
        isLoading.value = false
      }
    }



    const exportGame = async () => {
      try {
        isLoading.value = true
        loadingText.value = '保存中...'

        // 使用物品选择游戏专用的导出API
        const response = await itemSelectionApi.exportGameState(sessionId.value, items.value, playerName.value)
        const exportCode = response.data.exportCode

        // 显示保存成功对话框
        savedCode.value = exportCode
        showSaveSuccess.value = true
        toast.success('保存成功')

        console.log('保存成功，进度代码:', exportCode)
      } catch (error) {
        console.error('保存游戏失败:', error)
        toast.error('保存失败: ' + error.message)
      } finally {
        isLoading.value = false
      }
    }

    const importGame = async () => {
      if (!importCode.value.trim()) {
        toast.warning('请输入导入代码')
        return
      }

      try {
        isLoading.value = true
        loadingText.value = '导入中...'

        // 使用物品选择游戏专用的导入API
        await itemSelectionApi.importGameState(sessionId.value, importCode.value.trim())

        // 导入成功后重新加载游戏状态
        await loadGameState()

        toast.success('导入成功')
        closeImportDialog()

        console.log('导入成功，进度代码:', importCode.value.trim())
      } catch (error) {
        console.error('导入游戏失败:', error)
        toast.error('导入失败: ' + error.message)
      } finally {
        isLoading.value = false
      }
    }

    const closeImportDialog = () => {
      showImportDialog.value = false
      importCode.value = ''
    }

    const copySaveCode = async () => {
      try {
        await navigator.clipboard.writeText(savedCode.value)
        toast.success('代码已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        toast.error('复制失败，请手动复制代码')
      }
    }

    // 加载难度级别
    const loadDifficultyLevels = async () => {
      try {
        const response = await itemSelectionApi.getDifficultyLevels()
        if (response.data && response.data.levels) {
          difficultyLevels.value = response.data.levels
        }
      } catch (error) {
        console.error('加载难度级别失败:', error)
        // 设置默认难度级别
        difficultyLevels.value = [
          { level: 'normal', name: '普通版', description: '标准难度', isDefault: true },
          { level: 'hard', name: '困难版', description: '更具挑战性', isDefault: false }
        ]
      }
    }

    // 选择难度（左侧面板点击）
    const selectDifficulty = async (difficultyLevel) => {
      if (currentDifficulty.value === difficultyLevel) {
        return // 已经是当前难度，无需切换
      }

      if (!confirm('切换难度将重新开始游戏，确定要继续吗？')) {
        return
      }

      try {
        isLoading.value = true
        loadingText.value = '切换难度中...'

        // 创建新的游戏会话
        const response = await itemSelectionApi.createGameSession(playerName.value, difficultyLevel)

        if (response.data && response.data.sessionId) {
          sessionId.value = response.data.sessionId
          currentDifficulty.value = difficultyLevel

          // 加载新的游戏数据
          await loadGameState()

          const levelName = difficultyLevels.value.find(l => l.level === difficultyLevel)?.name || difficultyLevel
          toast.success(`已切换到${levelName}`)
        } else {
          throw new Error('创建新游戏会话失败')
        }
      } catch (error) {
        console.error('切换难度失败:', error)
        toast.error('切换难度失败: ' + error.message)
      } finally {
        isLoading.value = false
      }
    }

    // 获取难度图标
    const getDifficultyIcon = (level) => {
      switch (level) {
        case 'normal':
          return '🟢'
        case 'hard':
          return '🔴'
        default:
          return '⚪'
      }
    }



    // 分享游戏进度
    const shareGame = async () => {
      if (isSharing.value) return false

      try {
        isSharing.value = true

        // 先导出当前游戏进度
        const response = await itemSelectionApi.exportGameState(sessionId.value, items.value, playerName.value)
        const progressCode = response.data.exportCode

        // 导入分享工具
        const { shareGameProgress } = await import('../utils/shareUtils')

        // 生成物品选择游戏的分享链接
        const shareLink = generateItemSelectionShareLink(progressCode)
        const { copyToClipboard } = await import('../utils/clipboard')
        const success = await copyToClipboard(shareLink)

        if (success) {
          toast.success('分享链接已复制到剪贴板！\n其他人点击链接即可加载您的游戏进度', { duration: 3000 })
        } else {
          toast.error('复制失败，请手动复制链接', { duration: 3000 })
        }

        return success
      } catch (error) {
        console.error('分享失败:', error)
        toast.error('分享失败: ' + error.message)
        return false
      } finally {
        isSharing.value = false
      }
    }

    // 生成物品选择游戏的分享链接
    const generateItemSelectionShareLink = (progressCode) => {
      const baseUrl = window.location.origin
      return `${baseUrl}/?itemShare=${encodeURIComponent(progressCode)}`
    }

    const loadGameState = async () => {
      try {
        // 从物品选择游戏API加载游戏状态
        const response = await itemSelectionApi.getGameState(sessionId.value)
        const gameState = response.data

        // 物品选择游戏API返回格式：{ sessionId, playerName, items, pendingCount, confirmedCount, isCompleted }
        playerName.value = gameState.playerName

        if (gameState.items && Array.isArray(gameState.items)) {
          items.value = gameState.items.map(item => ({
            id: item.id,
            name: item.name,
            imagePath: item.imagePath,
            positionX: item.positionX,
            positionY: item.positionY,
            status: item.status || 'default'
          }))
        } else {
          // 没有物品数据，尝试从本地存储加载
          await loadFromLocalStorage()
        }
      } catch (error) {
        console.error('从后端加载游戏状态失败:', error)
        // 后端加载失败，尝试从本地存储加载
        await loadFromLocalStorage()
      }
    }

    const loadFromLocalStorage = async () => {
      try {
        const savedData = localStorage.getItem(`item-selection-${sessionId.value}`)
        if (savedData) {
          const gameData = JSON.parse(savedData)
          playerName.value = gameData.playerName || '本地玩家'
          items.value = gameData.items || []
          console.log('从本地存储加载游戏状态')
          toast.success('从本地存储恢复游戏状态')
        } else {
          // 没有本地数据，从后端获取默认物品
          await initializeDefaultItems()
        }
      } catch (error) {
        console.error('从本地存储加载失败:', error)
        // 本地存储也失败，从后端获取默认物品
        await initializeDefaultItems()
      }
    }

    const initializeDefaultItems = async () => {
      // 从后端获取默认物品数据
      try {
        const response = await itemSelectionApi.createGameSession('演示玩家')
        if (response.data && response.data.items) {
          // 物品选择游戏API返回的数据格式：{ sessionId, playerName, items }
          items.value = response.data.items.map(item => ({
            id: item.id,
            name: item.name,
            imagePath: item.imagePath,
            positionX: item.positionX,
            positionY: item.positionY,
            status: item.status || 'default'
          }))
          sessionId.value = response.data.sessionId
          playerName.value = response.data.playerName
        } else {
          console.error('无法获取默认物品数据')
          items.value = []
        }
      } catch (error) {
        console.error('获取默认物品数据失败:', error)
        // 如果后端完全不可用，显示空数组而不是硬编码数据
        items.value = []
      }
    }

    // 页面离开前保存
    const handleBeforeUnload = () => {
      if (hasUnsavedChanges.value) {
        // 转换为BoardStateDTO格式
        const boardStateDTO = {
          boardId: 1,
          width: 8,
          height: 8,
          items: items.value.map(item => ({
            id: item.id,
            itemId: item.id,
            name: item.name,
            x: item.positionX,
            y: item.positionY,
            width: 1,
            height: 1,
            imagePath: item.imagePath,
            isRemoved: item.status === 'confirmed'
          })),
          storedRed: { red6: 0, red9: 0, red12: 0 }
        }

        // 同步保存（页面卸载时异步操作可能不会完成）
        navigator.sendBeacon('/api/session/update', JSON.stringify({
          sessionId: sessionId.value,
          boardState: boardStateDTO,
          score: confirmedCount.value * 10 + pendingCount.value * 5,
          isCompleted: false
        }))
      }
    }

    // 初始化
    onMounted(async () => {
      // 首先加载难度级别
      await loadDifficultyLevels()

      sessionId.value = route.params.sessionId

      if (sessionId.value) {
        // 有sessionId时从后端加载游戏状态
        isLoading.value = true
        loadingText.value = '加载游戏状态...'

        try {
          await loadGameState()
        } finally {
          isLoading.value = false
        }
      } else {
        // 没有sessionId时，创建新的游戏会话
        isLoading.value = true
        loadingText.value = '创建新游戏...'

        try {
          await initializeDefaultItems()
        } finally {
          isLoading.value = false
        }
      }

      // 添加页面离开监听
      window.addEventListener('beforeunload', handleBeforeUnload)
    })

    // 清理
    onUnmounted(() => {
      // 移除事件监听
      window.removeEventListener('beforeunload', handleBeforeUnload)
    })

    return {
      sessionId,
      playerName,
      items,
      isLoading,
      loadingText,
      showImportDialog,
      importCode,
      showSaveSuccess,
      savedCode,
      hasUnsavedChanges,
      isSharing,
      difficultyLevels,
      currentDifficulty,
      currentDifficultyName,
      pendingCount,
      confirmedCount,
      getImageUrl,
      handleImageError,
      toggleItemStatus,
      confirmSelection,
      resetGame,
      exportGame,
      importGame,
      closeImportDialog,
      copySaveCode,
      selectDifficulty,
      getDifficultyIcon,

      shareGame
    }
  }
}
</script>

<style scoped>
.item-selection-game {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

.game-main {
  padding: 10px 5px 5px 5px;
  flex: 1;
  height: calc(100vh - 60px);
  overflow: hidden;
}

/* 导航栏内的玩家信息样式 */
.player-info {
  display: flex;
  align-items: center;
}

.player-name {
  color: #666;
  font-size: 14px;
  font-weight: 500;
}



.game-stats {
  display: flex;
  gap: 12px;
  align-items: center;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
}

.stat-label {
  color: #666;
}

.stat-value {
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.stat-value.pending {
  background: #4CAF50;
  color: white;
}

.stat-value.confirmed {
  background: #FF9800;
  color: white;
}

.stat-value.saved {
  background: #4CAF50;
  color: white;
}

.stat-value.unsaved {
  background: #f44336;
  color: white;
}

.difficulty-select {
  padding: 0.25rem 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.difficulty-select:hover {
  border-color: #4CAF50;
}

.difficulty-select:focus {
  outline: none;
  border-color: #4CAF50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

/* 游戏主体区域 */
.game-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.game-content-container {
  display: flex;
  gap: 0.5rem;
  flex: 1;
  justify-content: center;
  height: 100%;
  overflow: hidden;
}

/* 左侧难度选择面板 */
.difficulty-selector-panel {
  min-width: 160px;
  max-width: 180px;
  flex-shrink: 0;
}

.selector-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.selector-header {
  margin-bottom: 1rem;
  text-align: center;
}

.selector-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.selector-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.selector-option {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.selector-option:hover {
  border-color: #667eea;
  background: #f8f9ff;
}

.selector-option.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: #5a6fd8;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.option-icon {
  font-size: 1.5rem;
  margin-right: 0.75rem;
  min-width: 2rem;
  text-align: center;
}

.option-info {
  flex: 1;
}

.option-name {
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 0.2rem;
}

.option-desc {
  font-size: 0.75rem;
  opacity: 0.8;
}

/* 右侧游戏区域 */
.game-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  height: 100%;
  overflow-y: auto;
  padding: 0 5px;
}

.game-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 8px;
  margin-bottom: 20px;
  width: 100%;
  max-width: 100vw;
  padding: 0 10px;
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
}

.item-card {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  aspect-ratio: 1;
  width: 100%;
  box-shadow: 0 4px 15px rgba(0,0,0,0.15);
  border: 2px solid transparent;
  overflow: hidden;
  background-color: #f0f0f0;
  box-sizing: border-box;
}

.item-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 12px 35px rgba(0,0,0,0.3);
  border-color: rgba(102, 126, 234, 0.5);
}

.item-card.pending {
  border-color: #4CAF50;
  box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
}

.item-card.pending::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(76, 175, 80, 0.25);
  border-radius: 12px;
  z-index: 1;
}

.item-card.confirmed {
  border-color: #FF9800;
  box-shadow: 0 8px 25px rgba(255, 152, 0, 0.4);
}

.item-card.confirmed::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 152, 0, 0.25);
  border-radius: 12px;
  z-index: 1;
}

.status-label {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(76, 175, 80, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 0.7rem;
  font-weight: bold;
  z-index: 3;
  box-shadow: 0 2px 8px rgba(0,0,0,0.3);
  backdrop-filter: blur(4px);
}



.item-name {
  font-size: 0.75rem;
  font-weight: 600;
  text-align: center;
  line-height: 1.2;
  color: white;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  padding: 4px 8px;
  border-radius: 6px;
  word-break: break-word;
  hyphens: auto;
  z-index: 2;
  position: relative;
  margin: 4px;
  max-width: calc(100% - 8px);
}

.game-controls {
  display: flex;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 10px;
  padding: 0 10px;
}

.control-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;
}

.confirm-btn {
  background: #4CAF50;
  color: white;
}

.confirm-btn:hover:not(:disabled) {
  background: #45a049;
  transform: translateY(-2px);
}

.confirm-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.reset-btn {
  background: #f44336;
  color: white;
}

.reset-btn:hover {
  background: #da190b;
  transform: translateY(-2px);
}

.save-btn {
  background: #4CAF50;
  color: white;
}

.save-btn:hover {
  background: #45a049;
  transform: translateY(-2px);
}

.export-btn {
  background: #2196F3;
  color: white;
}

.export-btn:hover {
  background: #0b7dda;
  transform: translateY(-2px);
}

.import-btn {
  background: #FF9800;
  color: white;
}

.import-btn:hover {
  background: #e68900;
  transform: translateY(-2px);
}

.share-btn {
  background: #9C27B0;
  color: white;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.share-btn:hover {
  background: #7B1FA2;
  transform: translateY(-2px);
}

.share-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.share-icon {
  font-size: 1em;
}

.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  min-width: 400px;
  max-width: 90vw;
}

.dialog h3 {
  margin-bottom: 1rem;
  color: #333;
}

.dialog textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  margin-bottom: 1.5rem;
  resize: vertical;
}

.dialog textarea:focus {
  outline: none;
  border-color: #667eea;
}

.dialog-buttons {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.btn-cancel, .btn-confirm {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-cancel {
  background: #f5f5f5;
  color: #666;
}

.btn-cancel:hover {
  background: #e0e0e0;
}

.btn-confirm {
  background: #667eea;
  color: white;
}

.btn-confirm:hover:not(:disabled) {
  background: #5a6fd8;
}

.btn-confirm:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  z-index: 2000;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(255,255,255,0.3);
  border-top: 5px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.save-success {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.success-content {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  min-width: 400px;
  max-width: 90vw;
  text-align: center;
}

.success-content h3 {
  margin-bottom: 1rem;
  color: #4CAF50;
  font-size: 1.5rem;
}

.success-content p {
  margin-bottom: 1.5rem;
  color: #333;
  word-break: break-all;
}

.success-content strong {
  background: #f5f5f5;
  padding: 0.5rem;
  border-radius: 4px;
  font-family: monospace;
  font-size: 1.1rem;
}

.btn-copy, .btn-close {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 0 0.5rem;
}

.btn-copy {
  background: #4CAF50;
  color: white;
}

.btn-copy:hover {
  background: #45a049;
}

.btn-close {
  background: #f5f5f5;
  color: #666;
}

.btn-close:hover {
  background: #e0e0e0;
}

/* 响应式设计 - 保持8列布局 */
@media (max-width: 1200px) {
  .game-content-container {
    flex-direction: column;
    gap: 0.5rem;
  }

  .difficulty-selector-panel {
    min-width: auto;
    max-width: none;
    margin-bottom: 0.5rem;
  }

  .selector-options {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .selector-option {
    flex: 1;
    min-width: 120px;
  }

  .game-grid {
    gap: 6px;
    padding: 0 5px;
  }

  .item-name {
    font-size: 0.7rem;
    padding: 3px 6px;
  }
}

@media (max-width: 768px) {
  .selector-options {
    flex-direction: column;
  }

  .selector-option {
    min-width: auto;
  }

  .game-grid {
    gap: 4px;
    padding: 0 3px;
  }

  .item-name {
    font-size: 0.65rem;
    padding: 2px 4px;
  }

  .control-btn {
    padding: 6px 12px;
    font-size: 0.8rem;
    min-width: 70px;
  }
}

@media (max-width: 480px) {
  .game-grid {
    gap: 3px;
    padding: 0 2px;
  }

  .item-name {
    font-size: 0.6rem;
    padding: 2px 3px;
  }

  .control-btn {
    padding: 5px 10px;
    font-size: 0.75rem;
    min-width: 60px;
  }

  .game-controls {
    gap: 5px;
  }
}
</style>
